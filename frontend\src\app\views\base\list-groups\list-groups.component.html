<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <app-docs-components href="components/list-group" title="List Group" />
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Basic example</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          The default list group is an unordered list with items and the proper
          CSS classes. Build upon it with the options that follow, or with your
          CSS as required.
        </p>
        <app-docs-example href="components/list-group">
          <ul cListGroup>
            @for (item of sampleList; track item) {
              <li cListGroupItem>{{ item }}</li>
            }
          </ul>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Active items</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>active</code> boolean property to a
          <code>cListGroupItem</code> to show the current active selection.
        </p>
        <app-docs-example href="components/list-group/#active-items">
          <ul cListGroup>
            @for (item of sampleList; track item; let first = $first) {
              <li cListGroupItem [active]="first">{{ item }}</li>
            }
          </ul>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Disabled items</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>disabled</code> boolean property to a
          <code>cListGroupItem</code> to make it appear disabled.
        </p>
        <app-docs-example href="components/list-group/#disabled-items">
          <div cListGroup>
            @for (item of sampleList; track item; let first = $first) {
              <button cListGroupItem [disabled]="first">{{ item }}</button>
            }
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Links and buttons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use <code>&lt;a&gt;</code>s or <code>&lt;button&gt;</code>s to create
          <em>actionable</em> list group items with hover, disabled, and active
          states with <code>a</code> or <code>button</code>. We
          separate these pseudo-classes to ensure list groups made of
          non-interactive elements (like <code>&lt;li&gt;</code> or
          <code>&lt;div&gt;</code>) don't provide a click or tap affordance.
        </p>
        <app-docs-example href="components/list-group/#links-and-buttons">
          <div cListGroup>
            @for (item of sampleList; track item; let first = $first, last = $last) {
              <a href cListGroupItem [active]="first" [disabled]="last">{{ item }}</a>
            }
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Flush</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>flush</code> boolean property to remove some borders and
          rounded corners to render list group items edge-to-edge in a parent
          container (e.g., cards).
        </p>
        <app-docs-example href="components/list-group/#flush">
          <ul cListGroup flush>
            @for (item of sampleList; track item) {
              <li cListGroupItem>{{ item }}</li>
            }
          </ul>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Horizontal</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>layout=&#34;horizontal&#34;</code> to change the layout of
          list group items from vertical to horizontal across all breakpoints. <br>
          Alternatively, choose a responsive variant
          <code>[horizontal]="sm | md | lg | xl | xxl"</code>
          to make a list group horizontal starting at that breakpoint's
          <code>min-width</code>. <br>
          Currently <strong>horizontal list groups cannot be combined with flush list groups.</strong>
        </p>
        <app-docs-example href="components/list-group/#flush">
          @for (breakpoint of breakpoints; track breakpoint) {
            <ul
              [horizontal]="breakpoint"
              cListGroup
              class="mb-2"
            >
              <li cListGroupItem>Cras justo odio</li>
              <li cListGroupItem>Dapibus ac facilisis in</li>
              <li cListGroupItem>Morbi leo risus</li>
            </ul>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Contextual classes</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use contextual classes to style list items with a stateful background
          and color.
        </p>
        <app-docs-example href="components/list-group/#contextual-classes">
          <ul cListGroup class="mb-3">
            @for (color of colors; track color) {
              <li [color]="color" cListGroupItem>
                A simple {{ color }} list group item
              </li>
            }
          </ul>
        </app-docs-example>
        <p class="text-body-secondary small mt-1">
          Contextual classes also work with <code>&lt;a&gt;</code> or
          <code>&lt;button&gt;</code>. Note the addition of the hover styles
          here not present in the previous example. Also supported is the
          <code>active</code> state; apply it to indicate an active selection on
          a contextual list group item.
        </p>
        <app-docs-example href="components/list-group/#contextual-classes">
          <div cListGroup>
            @for (color of colors; track color) {
              <a [color]="color" cListGroupItem href>
                A simple {{ color }} list group item
              </a>
            }
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>With badges</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add badges to any list group item to show unread counts, activity, and
          more.
        </p>
        <app-docs-example href="components/list-group/#with-badges">
          <div cListGroup>
            @for (item of sampleList; track item; let i = $index, last = $last) {
              <button cListGroupItem class="d-flex justify-content-between align-items-center" [disabled]="last">
                {{ item }}
                <c-badge [color]="last ? 'secondary': 'primary'" shape="rounded-pill">{{ i + 1 }}</c-badge>
              </button>
            }
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Custom content</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add nearly any HTML within, even for linked list groups like the one
          below, with the help of
          <a href="https://coreui.io/docs/utilities/flex/">flexbox utilities</a
          >.
        </p>
        <app-docs-example href="components/list-group/#custom-content">
          <div cListGroup>
            <a [active]="true" cListGroupItem href>
              <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">List group item heading</h5>
                <small>3 days ago</small>
              </div>
              <p class="mb-1">
                Donec id elit non mi porta gravida at eget metus. Maecenas sed
                diam eget risus varius blandit.
              </p>
              <small>Donec id elit non mi porta.</small>
            </a>
            <a cListGroupItem href>
              <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">List group item heading</h5>
                <small class="text-body-secondary">3 days ago</small>
              </div>
              <p class="mb-1">
                Donec id elit non mi porta gravida at eget metus. Maecenas sed
                diam eget risus varius blandit.
              </p>
              <small class="text-body-secondary"
              >Donec id elit non mi porta.</small
              >
            </a>
            <a cListGroupItem href>
              <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">List group item heading</h5>
                <small class="text-body-secondary">3 days ago</small>
              </div>
              <p class="mb-1">
                Donec id elit non mi porta gravida at eget metus. Maecenas sed
                diam eget risus varius blandit.
              </p>
              <small class="text-body-secondary"
              >Donec id elit non mi porta.</small
              >
            </a>
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular List Group</strong> <small>Checkboxes and radios</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Place CoreUI&#39;s checkboxes and radios within list group items and
          customize as needed.
        </p>
        <app-docs-example href="components/list-group/#checkboxes-and-radios">
          <form (ngSubmit)="logValue()" [formGroup]="checkBoxes" cForm>
            <ul [flush]="true" cListGroup>
              <button (click)="setValue('one');" cListGroupItem type="button">
                <c-form-check>
                  <input cFormCheckInput formControlName="one" type="checkbox" />
                  <label cFormCheckLabel>Cras justo odio</label>
                </c-form-check>
              </button>
              <label cListGroupItem style="cursor: pointer;">
                <c-form-check>
                  <input cFormCheckInput formControlName="two" type="checkbox" />
                  <span class="ms-1">Dapibus ac facilisis in</span>
                </c-form-check>
              </label>
              <li cListGroupItem>
                <c-form-check>
                  <input cFormCheckInput formControlName="three" type="checkbox" />
                  <label cFormCheckLabel>Morbi leo risus</label>
                </c-form-check>
              </li>
              <li cListGroupItem>
                <c-form-check>
                  <input cFormCheckInput formControlName="four" type="checkbox" />
                  <label cFormCheckLabel>Orta ac consectetur ac</label>
                </c-form-check>
              </li>
              <li cListGroupItem>
                <c-form-check>
                  <input cFormCheckInput formControlName="five" type="checkbox" />
                  <label cFormCheckLabel>Vestibulum at eros</label>
                </c-form-check>
              </li>
            </ul>
            <button cButton class="mt-3" type="submit">Submit</button>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
