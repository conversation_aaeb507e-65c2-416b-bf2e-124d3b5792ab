import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private tokenKey = 'token';
  private userKey = 'user';
  private rolesKey = 'roles';

  // <PERSON>ie helpers (share across ports on localhost)
  private setCookie(name: string, value: string, days = 7) {
    const d = new Date();
    d.setTime(d.getTime() + days * 24 * 60 * 60 * 1000);
    const expires = 'expires=' + d.toUTCString();
    const path = 'path=/';
    // SameSite=Lax to allow across ports (same-site). No Secure on http.
    document.cookie = `${name}=${encodeURIComponent(
      value
    )}; ${expires}; ${path}; SameSite=Lax`;
  }

  private getCookie(name: string): string | null {
    const key = name + '=';
    const ca = document.cookie.split(';');
    for (let c of ca) {
      c = c.trim();
      if (c.indexOf(key) === 0) {
        return decodeURIComponent(c.substring(key.length, c.length));
      }
    }
    return null;
  }

  private deleteCookie(name: string) {
    // Set past expiry
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Lax`;
  }

  // Lưu thông tin đăng nhập (cookie-based)
  saveLogin(token: string, user: any, roles: string[]) {
    this.setCookie(this.tokenKey, token);
    this.setCookie(this.userKey, JSON.stringify(user));
    this.setCookie(this.rolesKey, JSON.stringify(roles));
  }

  // Lấy token
  getToken(): string | null {
    return this.getCookie(this.tokenKey);
  }

  // Lấy thông tin user
  getUser(): any {
    const user = this.getCookie(this.userKey);
    return user ? JSON.parse(user) : null;
  }

  // Lấy roles
  getRoles(): string[] {
    const roles = this.getCookie(this.rolesKey);
    return roles ? JSON.parse(roles) : [];
  }

  // Kiểm tra đã đăng nhập chưa
  isLoggedIn(): boolean {
    return !!this.getToken();
  }

  // Đăng xuất
  logout() {
    this.deleteCookie(this.tokenKey);
    this.deleteCookie(this.userKey);
    this.deleteCookie(this.rolesKey);
  }
}
