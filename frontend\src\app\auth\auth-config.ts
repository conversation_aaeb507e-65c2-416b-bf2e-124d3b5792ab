import { AuthConfig } from 'angular-oauth2-oidc';

// Minimal OAuth2/OIDC client configuration used by the app.
// Note: older code used `usePkce` which is not part of AuthConfig anymore.
// If you need PKCE ensure `disablePKCE` is false (default) or adjust options in the OAuthService calls.
export const authConfig: AuthConfig = {
    // issuer: 'https://your-issuer.example.com',
    // clientId: 'your-client-id',
    // redirectUri: window.location.origin + '/index.html',
    // responseType: 'code',
    // scope: 'openid profile email',
    // preserveRequestedRoute: true
};
