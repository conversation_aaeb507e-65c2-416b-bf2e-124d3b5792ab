name: Close Stale Issues and PRs

on:
  schedule:
    - cron: '0 0 1 * *' # Runs monthly

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - name: 🎭 Mask GitHub Token
        run: echo "::add-mask::${{ secrets.GITHUB_TOKEN }}"

      - name: Close stale issues and PRs 🕒
        uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'This issue has been automatically closed due to inactivity. Please reopen if you still need assistance.'
          stale-pr-message: 'This pull request has been automatically closed due to inactivity. Please reopen if you still need assistance.'
          days-before-stale: 30 # Number of days of inactivity before marking as stale
          days-before-close: 7 # Number of days to wait after marking as stale before closing
