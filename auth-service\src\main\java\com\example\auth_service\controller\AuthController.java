package com.example.auth_service.controller;


import com.example.auth_service.service.JwtService;

import com.example.auth_service.service.PermissionService;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;

@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:4201"}) // Cho phép truy cập từ 2 FE dev servers

public class AuthController {
    private final JwtService jwtService;
    private final GoogleIdTokenVerifier verifier;
    private final PermissionService permissionService;

    public AuthController(JwtService jwtService, @Value("${google.client-id}") String clientId, PermissionService permissionService) {
        this.jwtService = jwtService;
        this.permissionService = permissionService;
        this.verifier = new GoogleIdTokenVerifier.Builder(
                new com.google.api.client.http.javanet.NetHttpTransport(),
                new com.google.api.client.json.gson.GsonFactory()
        ).setAudience(java.util.Collections.singletonList(clientId)).build();
    }
    @PostMapping("/google")
    public ResponseEntity<Map<String, Object>> google(@RequestBody Map<String, Object> body) throws java.io.IOException, java.security.GeneralSecurityException {
        // Chấp nhận cả "idToken" (cũ) và "credential" (Google Identity Services)
        String idTokenStr = (String) (body.getOrDefault("idToken", body.get("credential")));
        if (idTokenStr == null || idTokenStr.isBlank()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "Missing idToken/credential"));
        }
        var idToken = verifier.verify(idTokenStr);
        if (idToken == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("error", "Invalid ID token"));
        }
        var payload = idToken.getPayload();
        String email = (String) payload.getEmail();
        String token = jwtService.generateToken(email);
    Map<String, Object> response = new HashMap<>();
    response.put("username", email);
    response.put("token", token);
    response.put("roles", permissionService.getRolesOfUser(email));
    return ResponseEntity.ok(response);
    }
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(
            @RequestParam String username,
            @RequestParam String password) {
        String token = generateJwtToken(username); // Hàm tạo JWT token

        // Tạo đối tượng JSON trả về
    Map<String, Object> response = new HashMap<>();
    response.put("username", username);
    response.put("token", token);
    response.put("roles", permissionService.getRolesOfUser(username));
    return ResponseEntity.ok(response);
    }

    private String generateJwtToken(String username) {
        
    return jwtService.generateToken(username); // Sử dụng JwtService để tạo token
    }

    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@RequestParam String username, @RequestParam String password) {
        if (username == null || username.trim().isEmpty() || password == null || password.length() < 6) {
            AuthResponse response = new AuthResponse(null, "Đăng ký thất bại - username/password không hợp lệ", null);
            return ResponseEntity.status(400).body(response);
        }
        try {
            // Đọc file user-roles.json
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            java.io.File file = new java.io.File("src/main/resources/user-roles.json");
            java.util.List<com.example.auth_service.model.UserRole> users = new java.util.ArrayList<>();
            if (file.exists()) {
                users = mapper.readValue(file, new com.fasterxml.jackson.core.type.TypeReference<java.util.List<com.example.auth_service.model.UserRole>>(){});
            }
            // Kiểm tra trùng username
            boolean exists = users.stream().anyMatch(u -> u.getUsername().equalsIgnoreCase(username));
            if (exists) {
                AuthResponse response = new AuthResponse(null, "Tên đăng nhập đã tồn tại", null);
                return ResponseEntity.status(409).body(response);
            }
            // Thêm user mới với role USER
            com.example.auth_service.model.UserRole newUser = new com.example.auth_service.model.UserRole();
            newUser.setUsername(username);
            newUser.setRoles(java.util.List.of("USER"));
            users.add(newUser);
            // Ghi lại file
            mapper.writerWithDefaultPrettyPrinter().writeValue(file, users);
            AuthResponse response = new AuthResponse(null, "Đăng ký thành công", username);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            AuthResponse response = new AuthResponse(null, "Đăng ký thất bại: " + e.getMessage(), null);
            return ResponseEntity.status(500).body(response);
        }
    }
    @RequestMapping(value = "/logout", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> logout() {
        // trả token expired bên client
        return ResponseEntity.ok("Đã đăng xuất thành công");
    }
}
