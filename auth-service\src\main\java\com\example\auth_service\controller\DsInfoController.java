package com.example.auth_service.controller;

import com.example.auth_service.multids.TenantContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/ds")
public class DsInfoController {
  private final JdbcTemplate jdbc;

  public DsInfoController(JdbcTemplate jdbc) {
    this.jdbc = jdbc;
  }

  @GetMapping("/whoami")
  public Map<String, Object> whoami() {
    String schema = jdbc.queryForObject("SELECT SCHEMA()", String.class);
    String user   = jdbc.queryForObject("SELECT USER()", String.class);
    return Map.of(
        "dsKey", TenantContext.get(),
        "schema", schema,
        "user", user
    );
  }
}