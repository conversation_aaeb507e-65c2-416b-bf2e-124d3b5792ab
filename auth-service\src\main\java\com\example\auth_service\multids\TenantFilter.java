package com.example.auth_service.multids;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Set;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TenantFilter extends OncePerRequestFilter {
  private static final String HEADER = "A";
  private static final Set<String> ALLOWED = Set.of("A1", "A2");

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
      throws ServletException, IOException {
    String key = request.getHeader(HEADER);

    if (key == null || key.isBlank()) {
      key = TenantContext.DEFAULT; // A1
    } else if (!ALLOWED.contains(key)) {
      // trả JSON lỗi + header gợi ý
      response.setHeader("X-DS-Allowed", String.join(",", ALLOWED));
      writeJsonError(response, HttpServletResponse.SC_BAD_REQUEST,
          "Unknown datasource key", key);
      return; // stop filter chain
    }

    TenantContext.set(key);
    // phản hồi DS thực tế để dễ debug
    response.setHeader("X-DS-Resolved", key);
    try {
      chain.doFilter(request, response);
    } finally {
      TenantContext.clear();
    }
  }

  private void writeJsonError(HttpServletResponse response, int status,
                              String title, String requested) throws IOException {
    response.setStatus(status);
    response.setContentType("application/json");
    response.setCharacterEncoding("UTF-8");
    String allowed = String.join("\",\"", ALLOWED);
    String json = String.format(
        "{\"error\":\"%s\",\"requested\":\"%s\",\"allowed\":[\"%s\"]}",
        title, requested, allowed
    );
    response.getWriter().write(json);
  }
}