package com.example.auth_service.service;

import com.example.auth_service.model.ApiPermission;
import com.example.auth_service.model.UserRole;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Service
public class PermissionService {
    private final List<UserRole> userRoles;
    private final List<ApiPermission> apiPermissions;

    public PermissionService() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        userRoles = mapper.readValue(new ClassPathResource("user-roles.json").getInputStream(), new TypeReference<List<UserRole>>(){});
        apiPermissions = mapper.readValue(new ClassPathResource("api-permissions.json").getInputStream(), new TypeReference<List<ApiPermission>>(){});
    }

    public List<String> getRolesOfUser(String username) {
        Optional<UserRole> user = userRoles.stream().filter(u -> u.getUsername().equals(username)).findFirst();
        return user.map(UserRole::getRoles).orElse(List.of());
    }

    public List<String> getRolesForApi(String api) {
        Optional<ApiPermission> perm = apiPermissions.stream().filter(p -> api.matches(p.getApi().replace("**", ".*"))).findFirst();
        return perm.map(ApiPermission::getRoles).orElse(List.of());
    }

    public boolean canAccess(String username, String api) {
        List<String> userRoles = getRolesOfUser(username);
        List<String> apiRoles = getRolesForApi(api);
        return userRoles.stream().anyMatch(apiRoles::contains);
    }
}
