import { inject } from '@angular/core';
import { Router, Routes } from '@angular/router';
import { AuthService } from './guards/auth.service';
import { RoleGuard } from './guards/role.guard'; // Đ<PERSON>m bảo đã tạo guard này
import { AdminComponent } from './views/pages/admin/admin.component'; // Đảm bảo đã có component này

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'unauthorized',
    loadComponent: () =>
      import('./views/pages/unauthorized/unauthorized.component').then(
        (m) => m.UnauthorizedComponent
      ),
    data: { title: 'Unauthorized' },
  },
  {
    path: '',
    loadComponent: () =>
      import('./layout').then((m) => m.DefaultLayoutComponent),
    data: {
      title: 'Home',
    },
    canActivate: [
      () => {
        const auth = inject(AuthService);
        const router = inject(Router);
        if (!auth.isLoggedIn()) {
          router.navigateByUrl('/login');
          return false;
        }
        return true;
      },
    ],
    children: [
      {
        path: 'dashboard',
        loadChildren: () =>
          import('./views/dashboard/routes').then((m) => m.routes),
      },
      {
        path: 'theme',
        loadChildren: () =>
          import('./views/theme/routes').then((m) => m.routes),
      },
      {
        path: 'base',
        loadChildren: () => import('./views/base/routes').then((m) => m.routes),
      },
      {
        path: 'buttons',
        loadChildren: () =>
          import('./views/buttons/routes').then((m) => m.routes),
      },
      {
        path: 'forms',
        loadChildren: () =>
          import('./views/forms/routes').then((m) => m.routes),
      },
      {
        path: 'icons',
        loadChildren: () =>
          import('./views/icons/routes').then((m) => m.routes),
      },
      {
        path: 'notifications',
        loadChildren: () =>
          import('./views/notifications/routes').then((m) => m.routes),
      },
      {
        path: 'widgets',
        loadChildren: () =>
          import('./views/widgets/routes').then((m) => m.routes),
      },
      {
        path: 'charts',
        loadChildren: () =>
          import('./views/charts/routes').then((m) => m.routes),
      },
      {
        path: 'pages',
        loadChildren: () =>
          import('./views/pages/routes').then((m) => m.routes),
      },
    ],
  },
  {
    path: '404',
    loadComponent: () =>
      import('./views/pages/page404/page404.component').then(
        (m) => m.Page404Component
      ),
    data: {
      title: 'Page 404',
    },
  },
  {
    path: '500',
    loadComponent: () =>
      import('./views/pages/page500/page500.component').then(
        (m) => m.Page500Component
      ),
    data: {
      title: 'Page 500',
    },
  },
  {
    path: 'login',
    loadComponent: () =>
      import('./views/pages/login/login.component').then(
        (m) => m.LoginComponent
      ),
    canMatch: [
      () => {
        const auth = inject(AuthService);
        const router = inject(Router);
        if (auth.isLoggedIn()) {
          router.navigateByUrl('/dashboard');
          return false;
        }
        return true;
      },
    ],
    data: {
      title: 'Login Page',
    },
  },
  {
    path: 'register',
    loadComponent: () =>
      import('./views/pages/register/register-simple.component').then(
        (m) => m.RegisterSimpleComponent
      ),
    data: {
      title: 'Register Page',
    },
  },
  {
    path: 'admin',
    component: AdminComponent,
    canActivate: [RoleGuard],
    data: { title: 'ADMIN', roles: ['ADMIN'] },
  },
  { path: '**', redirectTo: 'dashboard' },
];
