<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <app-docs-components href="components/tabs" title="Tabs" />
    <c-card class="mb-3">
      <c-card-header>
        <strong>Angular Tabs</strong> <small>underline</small>
      </c-card-header>
      <c-card-body>
        <c-tabs [activeItemKey]="0" (activeItemKeyChange)="handleActiveItemChange($event)">
          <c-tabs-list variant="underline-border">
            <button cTab [itemKey]="0">
              <svg cIcon class="me-2" name="cilHome"></svg>
              Home
            </button>
            <button cTab [itemKey]="1">
              <svg cIcon class="me-2" name="cilUser"></svg>
              Profile
            </button>
            <button cTab [itemKey]="2" [disabled]="activeItem()===0">
              <svg cIcon class="me-2" name="cilCode"></svg>
              Contact
            </button>
          </c-tabs-list>
          <c-tabs-content>
            <c-tab-panel [itemKey]="0" class="p-3">
              This is some placeholder content the <strong class="text-info">Home</strong> tab's associated content. Clicking another tab
              will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the
              content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation.
            </c-tab-panel>
            <c-tab-panel [itemKey]="1" class="p-3">
              This is some placeholder content the <strong class="text-success">Profile</strong> tab's associated content. Clicking another
              tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the
              content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation.
            </c-tab-panel>
            <c-tab-panel [itemKey]="2" class="p-3">
              This is some placeholder content the <strong class="text-warning">Contact</strong> tab's associated content. Clicking another
              tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the
              content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation.
            </c-tab-panel>
          </c-tabs-content>
        </c-tabs>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-3">
      <c-card-header>
        <strong>Angular Tabs</strong> <small>tabs</small>
      </c-card-header>
      <c-card-body>
        <c-tabs [activeItemKey]="0">
          <c-tabs-list variant="tabs">
            @for (tab of panes; track i; let i = $index, isLast = $last) {
              <button cTab [itemKey]="i" [disabled]="isLast">
                <svg cIcon class="me-2" [name]="tab.icon"></svg>
                {{ tab.name }}
              </button>
            }
          </c-tabs-list>
          <c-tabs-content>
            @for (pane of panes; track i; let i = $index) {
              <c-tab-panel class="p-3 preview" [itemKey]="i" cRounded="bottom">
                This is some placeholder content the <strong>{{ pane.name }}</strong> tab's associated content. Clicking
                another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to
                control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered
                navigation.
              </c-tab-panel>
            }
          </c-tabs-content>
        </c-tabs>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-3">
      <c-card-header>
        <strong>Angular Tabs</strong> <small>pills</small>
      </c-card-header>
      <c-card-body>
        <c-tabs>
          <c-tabs-list variant="pills">
            <button cTab [itemKey]="0">
              <svg cIcon class="me-2" name="cilHome"></svg>
              Home
            </button>
            <button cTab [itemKey]="1">
              <svg cIcon class="me-2" name="cilUser"></svg>
              Profile
            </button>
            <button cTab [itemKey]="2">
              <svg cIcon class="me-2" name="cilCode"></svg>
              Contact
            </button>
          </c-tabs-list>
          <c-tabs-content>
            <c-tab-panel [itemKey]="0" class="p-3">
              This is some placeholder content the <strong>Home</strong> tab's associated content. Clicking another tab
              will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the
              content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation.
            </c-tab-panel>
            <c-tab-panel [itemKey]="1" class="p-3">
              This is some placeholder content the <strong>Profile</strong> tab's associated content. Clicking another
              tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the
              content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation.
            </c-tab-panel>
            <c-tab-panel [itemKey]="2" class="p-3">
              This is some placeholder content the <strong>Contact</strong> tab's associated content. Clicking another
              tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the
              content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation.
            </c-tab-panel>
          </c-tabs-content>
        </c-tabs>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
