<c-callout class="bg-white:dark:bg-transparent" color="info">
  <ng-container *ngTemplateOutlet="defaultTpl" />
</c-callout>

<ng-template #defaultTpl>
  @let componentName = name();
  @let isPlural = plural();
  @if (!!componentName) {
    <p>
      An Angular {{ componentName }} component{{ isPlural ? 's' : '' }} {{ isPlural ? 'have' : 'has' }} been created as a native
      Angular
      version
      of Bootstrap {{ componentName }}. {{ componentName }} {{ isPlural ? 'are' : 'is' }} delivered with some new features,
      variants, and unique design that matches CoreUI Design System requirements.
    </p>
  }

  <ng-content />

  <br>
  For more information please visit our official <a href="{{href()}}" target="_blank">documentation of CoreUI Components Library for Angular.</a>
</ng-template>
