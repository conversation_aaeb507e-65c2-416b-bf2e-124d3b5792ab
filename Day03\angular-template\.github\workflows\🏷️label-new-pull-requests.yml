name: Label New Pull Requests

on:
  pull_request:
    types: [opened]

permissions:
  pull-requests: write

jobs:
  label:
    runs-on: ubuntu-latest
    steps:
      - name: 🎭 Mask GitHub Token
        run: echo "::add-mask::${{ secrets.GITHUB_TOKEN }}"

      - name: Add label to new pull request 🏷️
        uses: actions/github-script@v5
        with:
          script: |
            const pr_number = context.payload.pull_request.number;
            const repo = context.repo.repo;
            const owner = context.repo.owner;
            const labels = ['new-pr']; // Replace 'new-pr' with your desired label
            await github.rest.issues.addLabels({
              owner,
              repo,
              issue_number: pr_number,
              labels
            });
          github-token: ${{ secrets.GITHUB_TOKEN }}
