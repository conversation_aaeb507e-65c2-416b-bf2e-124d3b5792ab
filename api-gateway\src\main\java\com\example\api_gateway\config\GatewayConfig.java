package com.example.api_gateway.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class GatewayConfig {


    @Bean
    public RouteLocator routes(RouteLocatorBuilder builder) {
        return builder.routes()
            // Route đến auth-service, KHÔNG cần filter
            .route("auth-service", r -> r.path("/auth/**")
                .uri("http://localhost:8081")) // Cổng của auth service

            // Route đến các service khác, KHÔNG kiểm JWT ở gateway
            .route("user-service", r -> r.path("/user/**")
                .uri("http://localhost:8082"))

            // Ví dụ thêm route khác
            .route("tour-service", r -> r.path("/tour/**")
                .uri("http://localhost:8083"))

            .build();
    }
}

