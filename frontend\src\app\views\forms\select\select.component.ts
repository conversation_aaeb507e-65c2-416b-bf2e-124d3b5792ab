import { Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { CardBody<PERSON>omponent, CardComponent, CardHeaderComponent, ColComponent, FormSelectDirective, RowComponent } from '@coreui/angular';
import { DocsComponentsComponent, DocsExampleComponent } from '@docs-components/public-api';

@Component({
  selector: 'app-select',
  templateUrl: './select.component.html',
  imports: [RowComponent, ColComponent, CardComponent, CardH<PERSON>erComponent, CardBodyComponent, DocsExampleComponent, FormSelectDirective, ReactiveFormsModule, DocsComponentsComponent]
})
export class SelectComponent {}
