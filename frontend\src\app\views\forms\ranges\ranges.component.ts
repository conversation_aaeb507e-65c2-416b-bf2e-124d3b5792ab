import { Component } from '@angular/core';
import { <PERSON><PERSON>ody<PERSON><PERSON>ponent, Card<PERSON>omponent, CardHeaderComponent, ColComponent, FormControlDirective, FormLabelDirective, RowComponent } from '@coreui/angular';
import { DocsComponentsComponent, DocsExampleComponent } from '@docs-components/public-api';

@Component({
  selector: 'app-ranges',
  templateUrl: './ranges.component.html',
  imports: [RowComponent, ColComponent, CardComponent, CardHeaderComponent, CardBodyComponent, DocsExampleComponent, FormLabelDirective, FormControlDirective, DocsComponentsComponent]
})
export class RangesComponent {}
