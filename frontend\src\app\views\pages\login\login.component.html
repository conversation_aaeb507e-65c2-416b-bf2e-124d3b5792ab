<div
  class="bg-light dark:bg-transparent min-vh-100 d-flex flex-row align-items-center"
>
  <c-container breakpoint="md">
    <c-row class="justify-content-center">
      <c-col lg="10" xl="8">
        <c-card-group>
          <c-card class="p-4">
            <c-card-body>
              <!-- g<PERSON>i hàm login khi nhấn submit -->
              <form cForm (ngSubmit)="login()">
                <h1>Login</h1>
                <p class="text-body-secondary">Sign In to your account</p>
                <c-input-group class="mb-3">
                  <span cInputGroupText>
                    <svg cIcon name="cilUser"></svg>
                  </span>
                  <input
                    [(ngModel)]="username"
                    name="username"
                    autoComplete="username"
                    cFormControl
                    placeholder="Username"
                  />
                </c-input-group>
                <c-input-group class="mb-4">
                  <span cInputGroupText>
                    <svg cIcon name="cilLockLocked"></svg>
                  </span>
                  <input
                    [(ngModel)]="password"
                    name="password"
                    autoComplete="current-password"
                    cFormControl
                    placeholder="Password"
                    type="password"
                  />
                </c-input-group>
                <c-row>
                  <c-col xs="6">
                    <button cButton class="px-4" color="primary" type="submit">
                      Login
                    </button>
                  </c-col>
                  <c-col>
                    <!-- Placeholder for programmatic Google Sign-In button rendering -->
                    <!-- Custom Google Login Button -->
                    <div style="padding-top: 10px" id="googleSignInDiv"></div>
                    <div
                      id="g_id_onload"
                      data-client_id="773273370469-snmfmgnpdfg8l328pppqeulf8ud9gc4r.apps.googleusercontent.com"
                      data-context="signin"
                      data-ux_mode="popup"
                      data-login_uri="http://localhost:4200/#/login"
                      data-callback="handleCredentialResponse"
                      data-auto_prompt="false"
                    ></div>

                    <div
                      class="g_id_signin"
                      data-type="standard"
                      data-shape="pill"
                      data-theme="filled_blue"
                      data-text="signin_with"
                      data-size="large"
                      data-logo_alignment="left"
                    ></div>
                  </c-col>
                  <c-col class="text-end" xs="6">
                    <button cButton class="px-0" color="link">
                      Forgot password?
                    </button>
                  </c-col>
                </c-row>
              </form>
            </c-card-body>
          </c-card>
          <c-card
            [ngStyle]="{ 'minWidth.%': 44 }"
            class="text-white bg-primary py-5"
          >
            <c-card-body class="text-center">
              <div>
                <h2>Sign up</h2>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
                <button
                  [active]="true"
                  cButton
                  class="mt-3"
                  color="primary"
                  routerLink="/register"
                >
                  Register Now!
                </button>
              </div>
            </c-card-body>
          </c-card>
        </c-card-group>
        <c-row>
          <c-col>
            <button cButton color="secondary" (click)="testDs('A1')">
              Test DS A1
            </button>
            <button
              cButton
              color="secondary"
              (click)="testDs('A2')"
              class="ms-2"
            >
              Test DS A2
            </button>
            <pre *ngIf="dsInfo">{{ dsInfo | json }}</pre>
          </c-col>
        </c-row>
      </c-col>
    </c-row>
  </c-container>
</div>
