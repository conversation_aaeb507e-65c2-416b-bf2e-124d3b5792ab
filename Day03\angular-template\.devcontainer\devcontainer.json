{"name": "My Codespace", "image": "mcr.microsoft.com/vscode/devcontainers/typescript-node:0-20", "customizations": {"vscode": {"settings": {"terminal.integrated.shell.linux": "/bin/bash", "editor.formatOnSave": true, "editor.tabSize": 2}, "extensions": ["eamodio.gitlens", "codeandstuff.package-json-upgrade", "dbaeumer.vscode-eslint", "github.copilot", "github.copilot-chat", "humao.rest-client", "esbenp.prettier-vscode", "streetsidesoftware.code-spell-checker", "dzannotti.vscode-babel-coloring", "aaron-bond.better-comments", "pranaygp.vscode-css-peek", "ms-azuretools.vscode-docker", "editorconfig.editorconfig", "seatonjiang.gitmoji-vscode", "ecmel.vscode-html-css", "antfu.iconify", "ritwickdey.LiveServer", "ms-vsliveshare.vsliveshare", "christian-kohler.npm-intellisense", "ionutvmi.path-autocomplete", "yoavbls.pretty-ts-errors", "msjsdiag.vscode-react-native", "urbantrout.refactor-css", "simontest.simonte"]}}, "forwardPorts": [3000, 5000], "postCreateCommand": "npm install -g typescript nodemon && npm install && code --list-extensions", "mounts": ["source=/path/to/local/directory,target=/path/in/container,type=bind"]}