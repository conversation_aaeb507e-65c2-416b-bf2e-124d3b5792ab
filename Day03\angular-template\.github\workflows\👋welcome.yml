name: Welcome New Contributors

on:
  issues:
    types: [opened]

permissions:
  issues: write

jobs:
  welcome:
    runs-on: ubuntu-latest
    steps:
      - name: 🎭 Mask GitHub Token
        run: echo "::add-mask::${{ secrets.GITHUB_TOKEN }}"
      - name: Post welcome message 📨
        uses: actions/github-script@v5
        with:
          script: |
            const issue_number = context.issue.number;
            const repo = context.repo.repo;
            const owner = context.repo.owner;
            const body = ':tada: :sparkles: Hello, thanks for opening this issue! We will review it soon. :rocket:';
            await github.rest.issues.createComment({
              owner,
              repo,
              issue_number,
              body
            });
          github-token: ${{secrets.GITHUB_TOKEN}}
