<app-docs-components href="components/widgets" title="Widgets" />

<c-card class="mb-4">
  <c-card-header>Widgets</c-card-header>
  <c-card-body>
    <app-docs-example href="components/widgets/#cwidgetstatsa">
      <app-widgets-dropdown />
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsb">
      <c-row class="g-4">
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            [title]="'Widget title'"
            text="Lorem ipsum dolor sit amet enim."
            value="89.9%"
          >
            <c-progress class="my-2" thin [value]="89.9" color="success" />
          </c-widget-stat-b>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            [title]="'Widget title'"
            text="Lorem ipsum dolor sit amet enim."
            value="12.124"
          >
            <c-progress class="my-2" thin [value]="89.9" color="info" />
          </c-widget-stat-b>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            [title]="'Widget title'"
            text="Lorem ipsum dolor sit amet enim."
            value="$98,111.00"
          >
            <c-progress class="my-2" thin [value]="89.9" color="warning" />
          </c-widget-stat-b>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            [title]="'Widget title'"
            text="Lorem ipsum dolor sit amet enim."
            value="2 TB"
          >
            <c-progress class="my-2" thin [value]="89.9" color="primary" />
          </c-widget-stat-b>
        </c-col>
      </c-row>
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsb">
      <c-row class="g-4">
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            #widgetStatB1inv="cWidgetStatB"
            [title]="'Widget title'"
            color="success"
            inverse
            text="Lorem ipsum dolor sit amet enim."
            value="89.9%"
          >
            <c-progress [white]="widgetStatB1inv.inverse()" class="my-2" thin [value]="89.9" />
          </c-widget-stat-b>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            #widgetStatB2inv="cWidgetStatB"
            [title]="'Widget title'"
            color="info"
            inverse
            text="Lorem ipsum dolor sit amet enim."
            value="12.124"
          >
            <c-progress [white]="widgetStatB2inv.inverse()" class="my-2" thin [value]="89.9" />
          </c-widget-stat-b>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            #widgetStatB3inv="cWidgetStatB"
            [title]="'Widget title'"
            color="warning"
            inverse
            text="Lorem ipsum dolor sit amet enim."
            value="$98,111.00"
          >
            <c-progress [white]="widgetStatB3inv.inverse()" class="my-2" thin [value]="89.9" />
          </c-widget-stat-b>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-b
            #widgetStatB4inv="cWidgetStatB"
            [title]="'Widget title'"
            color="primary"
            inverse
            text="Lorem ipsum dolor sit amet enim."
            value="2 TB"
          >
            <c-progress [white]="widgetStatB4inv.inverse()" class="my-2" thin [value]="89.9" />
          </c-widget-stat-b>
        </c-col>
      </c-row>
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatse">
      <app-widgets-e />
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsf">
      <c-row class="g-4">
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="primary"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilSettings" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="info"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilUser" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="warning"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilMoon" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="danger"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilBell" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
      </c-row>
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsf">
      <c-row class="g-4">
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="primary"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilSettings" size="xl" width="24"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetFooterTemplate">
              <a class="font-weight-bold font-xs text-body-secondary"
                 href="https://coreui.io/"
                 rel="noopener norefferer"
                 target="_blank">
                View more
                <svg cIcon class="float-end" name="cilArrowRight" width="16"></svg>
              </a>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="info"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilUser" size="xl" width="24"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetFooterTemplate">
              <a class="font-weight-bold font-xs text-body-secondary"
                 href="https://coreui.io/"
                 rel="noopener norefferer"
                 target="_blank">
                View more
                <svg cIcon class="float-end" name="cilArrowRight" width="16"></svg>
              </a>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="warning"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilMoon" size="xl" width="24"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetFooterTemplate">
              <a class="font-weight-bold font-xs text-body-secondary"
                 href="https://coreui.io/"
                 rel="noopener norefferer"
                 target="_blank">
                View more
                <svg cIcon class="float-end" name="cilArrowRight" width="16"></svg>
              </a>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="danger"
            padding
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilBell" size="xl" width="24"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetFooterTemplate">
              <a class="font-weight-bold font-xs text-body-secondary"
                 href="https://coreui.io/"
                 rel="noopener norefferer"
                 target="_blank">
                View more
                <svg cIcon class="float-end" name="cilArrowRight" width="16"></svg>
              </a>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
      </c-row>
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsf">
      <c-row class="g-4">
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="primary"
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilSettings" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="info"
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilUser" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="warning"
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilMoon" size="xl" width="24"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
        <c-col xl="3" md="6" sm="6">
          <c-widget-stat-f
            [title]="'Income'"
            color="danger"
            value="$1,999.50"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon name="cilBell" size="xl" width="24" class="rounded-5"></svg>
            </ng-template>
          </c-widget-stat-f>
        </c-col>
      </c-row>
    </app-docs-example>

    <app-docs-example href="components/widgets/#cwidgetstatsd">
      <app-widgets-brand />
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsd">
      <app-widgets-brand [withCharts]="true" />
    </app-docs-example>

    <app-docs-example href="components/widgets/#cwidgetstatsc">
      <c-card-group class="mb-4">
        <c-widget-stat-c
          [title]="'Visitors'"
          value="87,500"
        >
          <ng-template cTemplateId="widgetIconTemplate">
            <svg cIcon height="36" name="cilPeople"></svg>
          </ng-template>
          <ng-template cTemplateId="widgetProgressTemplate">
            <c-progress class="mt-3 mb-0" thin [value]="75" color="info" />
          </ng-template>
        </c-widget-stat-c>
        <c-widget-stat-c
          [title]="'New Clients'"
          value="385"
        >
          <ng-template cTemplateId="widgetIconTemplate">
            <svg cIcon height="36" name="cilUserFollow"></svg>
          </ng-template>
          <ng-template cTemplateId="widgetProgressTemplate">
            <c-progress class="mt-3 mb-0" thin [value]="75" color="success" />
          </ng-template>
        </c-widget-stat-c>
        <c-widget-stat-c
          [title]="'Products sold'"
          value="1238"
        >
          <ng-template cTemplateId="widgetIconTemplate">
            <svg cIcon height="36" name="cilBasket"></svg>
          </ng-template>
          <ng-template cTemplateId="widgetProgressTemplate">
            <c-progress class="mt-3 mb-0" thin [value]="75" color="warning" />
          </ng-template>
        </c-widget-stat-c>
        <c-widget-stat-c
          [title]="'Returning Visitors'"
          value="28%"
        >
          <ng-template cTemplateId="widgetIconTemplate">
            <svg cIcon height="36" name="cilChartPie"></svg>
          </ng-template>
          <ng-template cTemplateId="widgetProgressTemplate">
            <c-progress class="mt-3 mb-0" thin [value]="75" color="primary" />
          </ng-template>
        </c-widget-stat-c>
        <c-widget-stat-c
          [title]="'Avg. Time'"
          value="5:34:11"
        >
          <ng-template cTemplateId="widgetIconTemplate">
            <svg cIcon height="36" name="cilSpeedometer"></svg>
          </ng-template>
          <ng-template cTemplateId="widgetProgressTemplate">
            <c-progress class="mt-3 mb-0" thin [value]="75" color="danger" />
          </ng-template>
        </c-widget-stat-c>
      </c-card-group>
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsc">
      <c-row class="g-4">
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Visitors'"
            value="87,500"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilPeople"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin [value]="75" color="info" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'New Clients'"
            value="385"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilUserFollow"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin [value]="75" color="success" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Products sold'"
            value="1238"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilBasket"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin [value]="75" color="warning" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Returning Visitors'"
            value="28%"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilChartPie"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin [value]="75" color="primary" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Avg. Time'"
            value="5:34:11"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilSpeedometer"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin [value]="75" color="danger" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Comments'"
            value="972"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilSpeech"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin [value]="75" color="dark" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
      </c-row>
    </app-docs-example>
    <app-docs-example href="components/widgets/#cwidgetstatsc">
      <c-row class="g-4">
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Visitors'"
            color="info"
            inverse
            value="87,500"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilPeople"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin white [value]="75" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'New Clients'"
            color="success"
            inverse
            value="385"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilUserFollow"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin white [value]="75" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Products sold'"
            color="warning"
            inverse
            value="1238"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilBasket"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin white [value]="75" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Returning Visitors'"
            color="primary"
            inverse
            value="28%"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilChartPie"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin white [value]="75" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Avg. Time'"
            color="danger"
            inverse
            value="5:34:11"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilSpeedometer"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin white [value]="75" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
        <c-col xl="2" lg="4" sm="6">
          <c-widget-stat-c
            [title]="'Comments'"
            color="dark"
            inverse
            value="972"
          >
            <ng-template cTemplateId="widgetIconTemplate">
              <svg cIcon height="36" name="cilSpeech"></svg>
            </ng-template>
            <ng-template cTemplateId="widgetProgressTemplate">
              <c-progress class="mt-3 mb-0" thin white [value]="75" />
            </ng-template>
          </c-widget-stat-c>
        </c-col>
      </c-row>
    </app-docs-example>
  </c-card-body>
</c-card>
