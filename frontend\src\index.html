<!DOCTYPE html>
<!--
* CoreUI - Angular 20.1 Free Admin Template
* @version v5.5.5
* @link https://coreui.io/angular/
* Copyright (c) 2017-2025 creativeLabs <PERSON><PERSON><PERSON>
* License: MIT
-->
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <base href="./" />
    <meta
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
      name="viewport"
    />
    <meta content="CoreUI Free Angular Admin Template" name="description" />
    <meta content="<PERSON><PERSON><PERSON>" name="author" />
    <meta
      content="CoreUI,Bootstrap,Admin,Template,Free,Angular,Dashboard,Typescript"
      name="keyword"
    />
    <link href="assets/favicon.ico" rel="icon" type="image/x-icon" />
    <title>CoreUI Free Angular Admin Template</title>
    <!-- Google Identity Services -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script>
      // Global callback for Google One Tap / Sign-In button
      window.handleCredentialResponse = function (response) {
        // response.credential is a JWT from Google
        console.log("Google credential:", response.credential);
        // TODO: gửi credential này về backend để verify và tạo phiên đăng nhập của bạn
      };
      // Set dev window title by port for demo with two sites
      (function () {
        try {
          var p = window.location.port;
          if (p === "4200") document.title = "Site A - CoreUI Angular";
          if (p === "4201") document.title = "Site B - CoreUI Angular";
        } catch (e) {}
      })();
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <app-root>
      <div
        class="fade show"
        style="text-align: center; padding-top: calc(100vh / 2); height: 100vh"
      >
        <i class="spinner-grow spinner-grow-sm"></i>
        <span class="m-1">Loading...</span>
      </div>
    </app-root>
  </body>
</html>
