# Dual Frontend Sites + Backend Demo

This repo is configured to run two Angular dev sites against the same backend services.

## Ports

- Frontend Site A: http://localhost:4200
- Frontend Site B: http://localhost:4201
- Auth Service: http://localhost:8080
- API Gateway: http://localhost:8081 (if used)

## Frontend

In `frontend/package.json` there are two scripts:

- `start` runs Site A on 4200
- `start:site-b` runs Site B on 4201

Both use `src/app/proxy.conf.json` to proxy `/auth` to `http://localhost:8080`.

## Backend CORS

- Auth Service allows CORS from 4200 and 4201.
- API Gateway CORS also allows both origins.

## Run (PowerShell)

```powershell
# Terminal 1 - Site A
cd frontend
npm install
npm run start

# Terminal 2 - Site B
cd frontend
npm run start:site-b

# Terminal 3 - Auth Service
cd ..\auth-service
./mvnw.cmd spring-boot:run

# Terminal 4 - API Gateway (optional)
cd ..\api-gateway
./mvnw.cmd spring-boot:run
```

Notes

- If ports are busy, change `--port` in the frontend scripts.
- For production, use a real domain and configure cookies/localStorage or SSO accordingly.
