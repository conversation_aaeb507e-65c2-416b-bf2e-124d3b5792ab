<div class="bg-light min-vh-100 d-flex flex-row align-items-center">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">
        <div class="card-group">
          <div class="card p-4">
            <div class="card-body">
              <form
                (ngSubmit)="register()"
                #registerForm="ngForm"
                autocomplete="off"
              >
                <h1>Register</h1>
                <p class="text-body-secondary">Tạo tài khoản mới</p>
                <div class="input-group mb-3">
                  <span class="input-group-text"
                    ><i class="bi bi-person"></i
                  ></span>
                  <input
                    id="username"
                    name="username"
                    [(ngModel)]="username"
                    required
                    class="form-control"
                    placeholder="Tên đăng nhập"
                  />
                </div>
                <div class="input-group mb-3">
                  <span class="input-group-text">@</span>
                  <input
                    id="email"
                    name="email"
                    [(ngModel)]="email"
                    type="email"
                    class="form-control"
                    placeholder="Email"
                  />
                </div>
                <div class="input-group mb-3">
                  <span class="input-group-text"
                    ><i class="bi bi-lock"></i
                  ></span>
                  <input
                    id="password"
                    name="password"
                    [(ngModel)]="password"
                    type="password"
                    required
                    class="form-control"
                    placeholder="Mật khẩu"
                  />
                </div>
                <div class="input-group mb-4">
                  <span class="input-group-text"
                    ><i class="bi bi-lock"></i
                  ></span>
                  <input
                    id="repeatPassword"
                    name="repeatPassword"
                    [(ngModel)]="repeatPassword"
                    type="password"
                    required
                    class="form-control"
                    placeholder="Nhập lại mật khẩu"
                  />
                </div>
                <div class="d-grid">
                  <button
                    class="btn btn-success"
                    [disabled]="loading"
                    type="submit"
                  >
                    Đăng ký
                  </button>
                </div>
              </form>
            </div>
          </div>
          <div
            class="card text-white bg-primary py-5 d-md-down-none"
            style="min-width: 44%"
          >
            <div class="card-body text-center">
              <div>
                <h2>Chào mừng!</h2>
                <p>
                  Đăng ký tài khoản để trải nghiệm đầy đủ các tính năng của hệ
                  thống.
                </p>

                <p class="mt-3">
                  Đã có tài khoản?
                  <a
                    routerLink="/login"
                    class="text-white text-decoration-underline"
                    >Đăng nhập</a
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          *ngIf="toastMsg"
          class="toast align-items-center text-bg-{{
            toastType === 'success' ? 'success' : 'danger'
          }} show"
          style="
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 9999;
            min-width: 220px;
          "
        >
          <div class="d-flex">
            <div class="toast-body">{{ toastMsg }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
