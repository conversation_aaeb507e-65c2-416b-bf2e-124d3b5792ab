<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <app-docs-components href="forms/select" title="Select" />
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Select</strong> <small>Default</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="forms/select">
          <select aria-label="Default select example" cSelect>
            <option>Open this select menu</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
          </select>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Select</strong> <small>Sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          You may also choose from small and large custom selects to match our similarly sized
          text inputs.
        </p>
        <app-docs-example href="forms/select#sizing">
          <select aria-label="Large select example" cSelect class="mb-3" sizing="lg">
            <option>Open this select menu</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
          </select>
          <select aria-label="Small select example" cSelect class="mb-3" sizing="sm">
            <option>Open this select menu</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
          </select>
        </app-docs-example>
        <p class="text-body-secondary small">
          The <code>multiple</code> attribute is also supported:
        </p>
        <app-docs-example href="forms/select#sizing">
          <select aria-label="Multiple select example" cSelect multiple sizing="lg">
            <option>Open this select menu</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
          </select>
        </app-docs-example>
        <p class="text-body-secondary small">
          As is the <code>html size</code> property:
        </p>
        <app-docs-example href="forms/select#sizing">
          <select aria-label="Multiple select example" cSelect multiple size="3" sizing="lg">
            <option>Open this select menu</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
            <option value="4">Four</option>
          </select>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Select</strong> <small>Disabled</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add the <code>disabled</code> boolean attribute on a select to give it a grayed out
          appearance and remove pointer events.
        </p>
        <app-docs-example href="forms/select#disabled">
          <select aria-label="Disabled select example" cSelect disabled>
            <option>Open this select menu</option>
            <option value="1">One</option>
            <option selected value="2">Selected: Two</option>
            <option value="3">Three</option>
          </select>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

