server:
  port: 8081 # Port riêng cho Auth Service

spring:
  application:
    name: auth-service
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        show_sql: false

google:
  client-id: 773273370469-snmfmgnpdfg8l328pppqeulf8ud9gc4r.apps.googleusercontent.com

app:
  datasource:
    a1:
      # H2 mem DB A1, tạo schema A1 và set làm current schema
      jdbc-url: jdbc:h2:mem:a1;MODE=LEGACY;DB_CLOSE_DELAY=-1;INIT=CREATE SCHEMA IF NOT EXISTS A1\;SET SCHEMA A1
      username: user_a1
      password:
      driver-class-name: org.h2.Driver
    a2:
      # H2 mem DB A2, tạo schema A2 và set làm current schema
      jdbc-url: jdbc:h2:mem:a2;MODE=LEGACY;DB_CLOSE_DELAY=-1;INIT=CREATE SCHEMA IF NOT EXISTS A2\;SET SCHEMA A2
      username: user_a2
      password:
      driver-class-name: org.h2.Driver
