2025-08-13 09:05:09.829256+07:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "MIGRATION_MODEL" not found (this database is empty); SQL statement:
SELECT ID, VERSION FROM MIGRATION_MODEL ORDER BY UPDATE_TIME DESC [42104-230]
2025-08-13 09:05:10.668384+07:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "DATABASECHANGELOG" not found (this database is empty); SQL statement:
SELECT COUNT(*) FROM PUBLIC.DATABASECHANGELOG [42104-230]
2025-08-13 09:05:10.938588+07:00 jdbc[4]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "DATABASECHANGELOGLOCK" not found (this database is empty); SQL statement:
SELECT COUNT(*) FROM PUBLIC.DATABASECHANGELOGLOCK [42104-230]
2025-08-13 09:05:10.990588+07:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "DATABASECHANGELOG" not found; SQL statement:
SELECT COUNT(*) FROM PUBLIC.DATABASECHANGELOG [42102-230]
2025-08-13 09:05:13.921269+07:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Column "T.DETAILS_JSON" not found; SQL statement:
select t.DETAILS_JSON from PUBLIC.ADMIN_EVENT_ENTITY t where 0=1 [42122-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.expression.ExpressionColumn.getColumnException(ExpressionColumn.java:244)
	at org.h2.expression.ExpressionColumn.optimizeOther(ExpressionColumn.java:226)
	at org.h2.expression.ExpressionColumn.optimize(ExpressionColumn.java:213)
	at org.h2.command.query.Select.prepareExpressions(Select.java:1228)
	at org.h2.command.query.Query.prepare(Query.java:232)
	at org.h2.command.Parser.prepareCommand(Parser.java:489)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:644)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:560)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:315)
	at io.agroal.pool.wrapper.ConnectionWrapper.prepareStatement(ConnectionWrapper.java:649)
	at liquibase.database.jvm.JdbcConnection.prepareStatement(JdbcConnection.java:443)
	at liquibase.precondition.core.ColumnExistsPrecondition.makeSureColumnExistsInOtherDBs(ColumnExistsPrecondition.java:161)
	at liquibase.precondition.core.ColumnExistsPrecondition.checkFast(ColumnExistsPrecondition.java:140)
	at liquibase.precondition.core.ColumnExistsPrecondition.check(ColumnExistsPrecondition.java:83)
	at liquibase.precondition.core.NotPrecondition.check(NotPrecondition.java:34)
	at liquibase.precondition.core.AndPrecondition.check(AndPrecondition.java:39)
	at liquibase.precondition.core.PreconditionContainer.check(PreconditionContainer.java:213)
	at liquibase.changelog.ChangeSet.execute(ChangeSet.java:689)
	at liquibase.changelog.visitor.UpdateVisitor.executeAcceptedChange(UpdateVisitor.java:119)
	at liquibase.changelog.visitor.UpdateVisitor.visit(UpdateVisitor.java:68)
	at liquibase.changelog.ChangeLogIterator.lambda$run$0(ChangeLogIterator.java:131)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.changelog.ChangeLogIterator.lambda$run$1(ChangeLogIterator.java:120)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.Scope.child(Scope.java:257)
	at liquibase.Scope.child(Scope.java:261)
	at liquibase.changelog.ChangeLogIterator.run(ChangeLogIterator.java:89)
	at liquibase.command.core.AbstractUpdateCommandStep.lambda$run$0(AbstractUpdateCommandStep.java:113)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.command.core.AbstractUpdateCommandStep.run(AbstractUpdateCommandStep.java:111)
	at liquibase.command.core.UpdateCommandStep.run(UpdateCommandStep.java:105)
	at liquibase.command.CommandScope.execute(CommandScope.java:220)
	at liquibase.Liquibase.lambda$update$0(Liquibase.java:216)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.Liquibase.runInScope(Liquibase.java:1290)
	at liquibase.Liquibase.update(Liquibase.java:205)
	at liquibase.Liquibase.update(Liquibase.java:188)
	at liquibase.Liquibase.update(Liquibase.java:175)
	at org.keycloak.quarkus.runtime.storage.database.liquibase.QuarkusJpaUpdaterProvider.updateChangeSet(QuarkusJpaUpdaterProvider.java:190)
	at org.keycloak.quarkus.runtime.storage.database.liquibase.QuarkusJpaUpdaterProvider.update(QuarkusJpaUpdaterProvider.java:105)
	at org.keycloak.quarkus.runtime.storage.database.liquibase.QuarkusJpaUpdaterProvider.update(QuarkusJpaUpdaterProvider.java:83)
	at org.keycloak.quarkus.runtime.storage.database.jpa.QuarkusJpaConnectionProviderFactory.update(QuarkusJpaConnectionProviderFactory.java:281)
	at org.keycloak.quarkus.runtime.storage.database.jpa.QuarkusJpaConnectionProviderFactory.createOrUpdateSchema(QuarkusJpaConnectionProviderFactory.java:247)
	at org.keycloak.quarkus.runtime.storage.database.jpa.QuarkusJpaConnectionProviderFactory.postInit(QuarkusJpaConnectionProviderFactory.java:124)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:167)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:164)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:164)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:164)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:164)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:164)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initProviderFactories(DefaultKeycloakSessionFactory.java:141)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initProviderFactories(DefaultKeycloakSessionFactory.java:125)
	at org.keycloak.quarkus.runtime.integration.QuarkusKeycloakSessionFactory.init(QuarkusKeycloakSessionFactory.java:87)
	at org.keycloak.quarkus.runtime.integration.jaxrs.QuarkusKeycloakApplication.createSessionFactory(QuarkusKeycloakApplication.java:67)
	at org.keycloak.services.resources.KeycloakApplication.startup(KeycloakApplication.java:74)
	at org.keycloak.quarkus.runtime.integration.jaxrs.QuarkusKeycloakApplication.onStartupEvent(QuarkusKeycloakApplication.java:52)
	at org.keycloak.quarkus.runtime.integration.jaxrs.QuarkusKeycloakApplication_Observer_onStartupEvent_GNZ8m5QenZ9h9VNelo7awjUZFDE.notify(Unknown Source)
	at io.quarkus.arc.impl.EventImpl$Notifier.notifyObservers(EventImpl.java:365)
	at io.quarkus.arc.impl.EventImpl$Notifier.notify(EventImpl.java:347)
	at io.quarkus.arc.impl.EventImpl.fire(EventImpl.java:81)
	at io.quarkus.arc.runtime.ArcRecorder.fireLifecycleEvent(ArcRecorder.java:163)
	at io.quarkus.arc.runtime.ArcRecorder.handleLifecycleEvents(ArcRecorder.java:114)
	at io.quarkus.runner.recorded.LifecycleEventsBuildStep$startupEvent1144526294.deploy_0(Unknown Source)
	at io.quarkus.runner.recorded.LifecycleEventsBuildStep$startupEvent1144526294.deploy(Unknown Source)
	at io.quarkus.runner.ApplicationImpl.doStart(Unknown Source)
	at io.quarkus.runtime.Application.start(Application.java:101)
	at io.quarkus.runtime.ApplicationLifecycleManager.run(ApplicationLifecycleManager.java:121)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:77)
	at org.keycloak.quarkus.runtime.KeycloakMain.start(KeycloakMain.java:133)
	at org.keycloak.quarkus.runtime.cli.Picocli.start(Picocli.java:1014)
	at org.keycloak.quarkus.runtime.cli.command.AbstractStartCommand.run(AbstractStartCommand.java:49)
	at picocli.CommandLine.executeUserObject(CommandLine.java:2030)
	at picocli.CommandLine.access$1500(CommandLine.java:148)
	at picocli.CommandLine$RunLast.executeUserObjectOfLastSubcommandWithSameParent(CommandLine.java:2465)
	at picocli.CommandLine$RunLast.handle(CommandLine.java:2457)
	at picocli.CommandLine$RunLast.handle(CommandLine.java:2419)
	at picocli.CommandLine$AbstractParseResultHandler.execute(CommandLine.java:2277)
	at picocli.CommandLine$RunLast.execute(CommandLine.java:2421)
	at picocli.CommandLine.execute(CommandLine.java:2174)
	at org.keycloak.quarkus.runtime.cli.Picocli.parseAndRun(Picocli.java:130)
	at org.keycloak.quarkus.runtime.KeycloakMain.main(KeycloakMain.java:104)
	at org.keycloak.quarkus.runtime.KeycloakMain.main(KeycloakMain.java:78)
	at io.quarkus.bootstrap.runner.QuarkusEntryPoint.doRun(QuarkusEntryPoint.java:68)
	at io.quarkus.bootstrap.runner.QuarkusEntryPoint.main(QuarkusEntryPoint.java:36)
