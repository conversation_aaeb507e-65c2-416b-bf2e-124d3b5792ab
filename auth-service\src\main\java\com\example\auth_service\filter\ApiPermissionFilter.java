package com.example.auth_service.filter;

import com.example.auth_service.service.PermissionService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;
import java.io.IOException;

@Component
public class ApiPermissionFilter extends OncePerRequestFilter {
    private final PermissionService permissionService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public ApiPermissionFilter(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain chain)
            throws ServletException, IOException {

        String path = request.getRequestURI();
        String method = request.getMethod();

        // Skip preflight và các endpoint auth
        if ("OPTIONS".equalsIgnoreCase(method) || pathMatcher.match("/auth/**", path)) {
            chain.doFilter(request, response);
            return;
        }

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = (auth != null && auth.getPrincipal() != null) ? auth.getName() : null;

        // Bỏ qua các endpoint public
        if (path.startsWith("/auth") || path.startsWith("/h2-console")) {
            chain.doFilter(request, response);
            return;
        }

        if (username != null && permissionService.canAccess(username, path)) {
            chain.doFilter(request, response);
        } else {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.getWriter().write("Forbidden: You do not have permission to access this resource");
        }
    }
}
