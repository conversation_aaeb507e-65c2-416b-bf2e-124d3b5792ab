import { <PERSON>son<PERSON><PERSON><PERSON>, NgI<PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'; // Import HttpClient and HttpParams
import { AfterViewInit, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms'; // Import FormsModule
import { Router, RouterModule } from '@angular/router';
import {
  ButtonDirective,
  CardBodyComponent,
  CardComponent,
  CardGroupComponent,
  ColComponent,
  ContainerComponent,
  FormControlDirective,
  FormDirective,
  InputGroupComponent,
  InputGroupTextDirective,
  RowComponent,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { AuthService } from '../../../guards/auth.service';
declare const google: any;
@Component({
  selector: 'app-login',
  standalone: true, // Add this line
  templateUrl: './login.component.html',
  imports: [
    ContainerComponent,
    RowComponent,
    ColComponent,
    CardGroupComponent,
    CardComponent,
    CardBodyComponent,
    FormDirective,
    InputGroupComponent,
    InputGroupTextDirective,
    IconDirective,
    FormControlDirective,
    ButtonDirective,
    NgStyle,
    NgIf, // <-- add
    JsonPipe, // <-- add
    FormsModule,
    RouterModule,
  ], // Add FormsModule here
})
export class LoginComponent implements OnInit, AfterViewInit {
  username = '';
  password = '';
  dsInfo?: any; // chỉ để hiển thị kết quả test

  constructor(
    private http: HttpClient,
    private router: Router,
    private auth: AuthService
  ) { }

  ngOnInit(): void {
    // Nếu đã đăng nhập (có token trong cookie), chuyển thẳng vào dashboard
    if (this.auth.isLoggedIn()) {
      this.router.navigateByUrl('/dashboard');
      return;
    }
    // Gắn callback global cho GIS
    (window as any).handleCredentialResponse = (resp: any) => {
      const idToken = resp?.credential;
      if (!idToken) return; // <-- avoid calling API when missing
      this.http
        .post<{ token: string; username: string; roles: string[] }>(
          '/auth/google',
          { idToken }
        )
        .subscribe({
          next: (data) => {
            if (data?.token) {
              this.auth.saveLogin(
                data.token,
                { username: data.username },
                data.roles || []
              );
            }
            // điều hướng sau khi đăng nhập thành công
            this.router.navigateByUrl('/dashboard');
          },
          error: (err) => {
            console.error('Google login failed', err);
          },
        });
    };
  }

  ngAfterViewInit(): void {
    try {
      google?.accounts?.id?.initialize({
        client_id:
          '************-snmfmgnpdfg8l328pppqeulf8ud9gc4r.apps.googleusercontent.com',
        callback: (resp: any) => (window as any).handleCredentialResponse(resp),
        ux_mode: 'popup',
      });
      const el = document.getElementById('googleSignInDiv');
      if (el) {
        google.accounts.id.renderButton(el, {
          theme: 'filled_blue',
          shape: 'pill',
          size: 'large',
        });
      }
    } catch { }
  }

  // Form login thường với username/password
  login(): void {
    const url = '/auth/login';
    let params = new HttpParams()
      .set('username', this.username)
      .set('password', this.password);

    this.http
      .post<{ token: string; username: string; roles: string[] }>(
        url,
        {},
        { params }
      )
      .subscribe({
        next: (data) => {
          if (data?.token) {
            this.auth.saveLogin(
              data.token,
              { username: data.username },
              data.roles
            );
          }
          this.router.navigateByUrl('/dashboard');
        },
        error: (err) => {
          console.error('Password login failed', err);
        },
      });
  }

  testDs(key: 'A1' | 'A2' | any) {
    const headers = new HttpHeaders().set('A', key);
    this.http
      .get<{ dsKey: string; schema: string; user: string }>('/ds/whoami', {
        headers,
      })
      .subscribe({
        next: (res) => {
          this.dsInfo = res;
        },
        error: (err) => {
          const msg = err.error?.error || err.error || err.message;
          this.dsInfo = {
            status: err.status,
            message: msg,
            requested: key,
            allowed: err.headers?.get('X-DS-Allowed'),
          };
          console.error('whoami error', this.dsInfo);
        },
      });
  }

  isAdmin(): boolean {
    return this.auth.getRoles().includes('ADMIN');
  }

  hasRole(role: string): boolean {
    return this.auth.getRoles().includes(role);
  }
}
