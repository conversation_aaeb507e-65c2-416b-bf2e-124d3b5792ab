package com.example.auth_service.multids;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DataSourceConfig {

  @Bean
  @ConfigurationProperties("app.datasource.a1")
  public DataSource a1DataSource() {
    return new HikariDataSource();
  }

  @Bean
  @ConfigurationProperties("app.datasource.a2")
  public DataSource a2DataSource() {
    return new HikariDataSource();
  }

  @Bean
  @Primary
  public DataSource routingDataSource(
      @Qualifier("a1DataSource") DataSource a1,
      @Qualifier("a2DataSource") DataSource a2) {
    MultiTenantRoutingDataSource routing = new MultiTenantRoutingDataSource();
    Map<Object, Object> targets = new HashMap<>();
    targets.put("A1", a1);
    targets.put("A2", a2);
    routing.setTargetDataSources(targets);
    routing.setDefaultTargetDataSource(a1);
    routing.afterPropertiesSet();
    return routing;
  }

  @Bean
  public JdbcTemplate jdbcTemplate(@Qualifier("routingDataSource") DataSource ds) {
    return new JdbcTemplate(ds);
  }
}