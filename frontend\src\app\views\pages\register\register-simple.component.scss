.register-page-center {
  min-height: 50vh;
  width: 50vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1;
}

.register-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 18px;
}

.register-logo img {
  height: 56px;
  width: auto;
  object-fit: contain;
  filter: drop-shadow(0 2px 8px rgba(44, 62, 80, 0.08));
}

.register-wrapper {
  max-width: 420px;
  width: 100%;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.08);
  padding: 32px 24px 24px 24px;
}

.register-wrapper h2 {
  text-align: center;
  margin-bottom: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 6px;
  color: #34495e;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #dbeafe;
  padding: 10px 12px;
  font-size: 1rem;
  margin-bottom: 8px;
}

.btn-success {
  background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
  border: none;
  font-weight: 600;
  font-size: 1.1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(67, 233, 123, 0.08);
  transition: background 0.2s;
}

.btn-success:disabled {
  opacity: 0.7;
}

@media (max-width: 600px) {
  .register-wrapper {
    padding: 18px 6px 12px 6px;
    max-width: 98vw;
  }

  .register-wrapper h2 {
    font-size: 1.3rem;
  }
}
