package com.example.auth_service.multids;

public final class TenantContext {
    public static final String DEFAULT = "A1";
    private static final ThreadLocal<String> CURRENT = new ThreadLocal<>();

    private TenantContext() {}

    public static void set(String key) { CURRENT.set(key); }
    public static String get() { return CURRENT.get() != null ? CURRENT.get() : DEFAULT; }
    public static void clear() { CURRENT.remove(); }
}