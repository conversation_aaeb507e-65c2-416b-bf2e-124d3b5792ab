<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <app-docs-components href="components/button" title="Button" />
    <c-card class="mb-4" id="AngularButton">
      <c-card-header>
        <strong>Angular Button</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          CoreUI includes a bunch of predefined buttons components, each serving its own
          semantic purpose. Buttons show what action will happen when the user clicks or touches
          it. CoreUI buttons are used to initialize operations, both in the background or
          foreground of an experience.
        </p>
        <app-docs-example fragment="AngularButton" href="components/button">
          @for (state of states; track state; let i = $index) {
            <c-row class="align-items-center mb-3">
              <c-col class="mb-3 mb-xl-0" xl="2" xs="12">
                {{ state.charAt(0).toUpperCase() + state.slice(1) }}
              </c-col>
              <c-col>
                @for (color of colors; track color; let i = $index) {
                  <button
                    [active]="state === 'active'"
                    [color]="color"
                    [disabled]="state === 'disabled'"
                    cButton
                  >
                    {{ color.charAt(0).toUpperCase() + color.slice(1) }}
                  </button>
                }
                <button [active]="state === 'active'" [disabled]="state === 'disabled'" cButton color="link">Link
                </button>
              </c-col>
            </c-row>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4" id="AngularButtonWithIcons">
      <c-card-header>
        <strong>Angular Button</strong> <small>with icons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          You can combine button with our <a href="https://icons.coreui.io/">CoreUI Icons</a>.
        </p>
        <app-docs-example fragment="AngularButtonWithIcons" href="components/button">
          @for (state of states; track state; let i = $index) {
            <c-row class="align-items-center mb-3">
              <c-col class="mb-3 mb-xl-0" xl="2" xs="12">
                {{ state.charAt(0).toUpperCase() + state.slice(1) }}
              </c-col>
              <c-col>
                @for (color of colors; track color; let i = $index) {
                  <button
                    [active]="state === 'active'"
                    [color]="color"
                    [disabled]="state === 'disabled'"
                    cButton
                  >
                    <svg cIcon class="me-2" name="cil-bell"></svg>
                    {{ color.charAt(0).toUpperCase() + color.slice(1) }}
                  </button>
                }
                <button [active]="state === 'active'" [disabled]="state === 'disabled'" cButton color="link">
                  <svg cIcon class="me-2" name="cil-bell"></svg>
                  Link
                </button>
              </c-col>
            </c-row>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>Button components</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          The <code>&lt;button&gt;</code> component are designed for
          <code>&lt;button&gt;</code> , <code>&lt;a&gt;</code> or <code>&lt;input&gt;</code>
          elements (though some browsers may apply a slightly different rendering).
        </p>
        <p class="text-body-secondary small">
          If you&#39;re using <code>&lt;button&gt;</code> component as <code>&lt;a&gt;</code>
          elements that are used to trigger functionality ex. collapsing content, these links
          should be given a <code>role=&#34;button&#34;</code> to adequately communicate their
          meaning to assistive technologies such as screen readers.
        </p>
        <app-docs-example href="components/button#button-components">
          <a [routerLink]="[]" cButton class="me-1" color="primary">
            Link
          </a>
          <button cButton class="me-1" color="primary" type="submit">
            Button
          </button>
          <input cButton class="me-1" color="primary" type="button" value="Input">
          <input cButton class="me-1" color="primary" type="submit" value="Submit">
          <input cButton class="me-1" color="primary" type="reset" value="Reset">
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>outline</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          If you need a button, but without the strong background colors. Set
          <code>variant=&#34;outline&#34;</code> prop to remove all background colors.
        </p>
        <app-docs-example href="components/button#outline-buttons">
          @for (state of states; track state; let i = $index) {
            <c-row class="align-items-center mb-3">
              <c-col class="mb-3 mb-xl-0" xl="2" xs="12">
                {{ state.charAt(0).toUpperCase() + state.slice(1) }}
              </c-col>
              <c-col>
                @for (color of colors; track color; let i = $index) {
                  <button
                    [active]="state === 'active'"
                    [color]="color"
                    [disabled]="state === 'disabled'"
                    cButton
                    variant="outline"
                  >
                    {{ color.charAt(0).toUpperCase() + color.slice(1) }}
                  </button>
                }
              </c-col>
            </c-row>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>ghost</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          If you need a ghost variant of button, set <code>variant=&#34;ghost&#34;</code> prop
          to remove all background colors.
        </p>
        <app-docs-example href="components/button#ghost-buttons">
          @for (state of states; track state; let i = $index) {
            <c-row class="align-items-center mb-3">
              <c-col class="mb-3 mb-xl-0" xl="2" xs="12">
                {{ state.charAt(0).toUpperCase() + state.slice(1) }}
              </c-col>
              <c-col>
                @for (color of colors; track color; let i = $index) {
                  <button
                    [active]="state === 'active'"
                    [color]="color"
                    [disabled]="state === 'disabled'"
                    cButton
                    variant="ghost"
                  >
                    {{ color.charAt(0).toUpperCase() + color.slice(1) }}
                  </button>
                }
              </c-col>
            </c-row>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>Sizes</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Larger or smaller buttons? Add <code>size=&#34;lg&#34;</code>
          <code>size=&#34;sm&#34;</code> for additional sizes.
        </p>
        <app-docs-example href="components/button#sizes">
          <button cButton class="mb-3" color="primary" size="lg">
            Large button
          </button>
          <button cButton class="mb-3" color="secondary" size="lg">
            Large button
          </button>
        </app-docs-example>
        <app-docs-example href="components/button#sizes">
          <br>
          <button cButton class="mb-3" color="primary" size="sm">
            Small button
          </button>
          <button cButton class="mb-3" color="secondary" size="sm">
            Small button
          </button>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>Pill</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/button#pill-buttons">
          @for (color of colors; track color; let i = $index) {
            <button
              [color]="color"
              cButton
              class="me-1"
              shape="rounded-pill"
            >
              {{ color.charAt(0).toUpperCase() + color.slice(1) }}
            </button>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>Square</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/button#square">
          @for (color of colors; track color; let i = $index) {
            <button
              [color]="color"
              cButton
              class="me-1"
              shape="rounded-0"
            >
              {{ color.charAt(0).toUpperCase() + color.slice(1) }}
            </button>
          }
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>Disabled state</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add the <code>disabled</code> boolean prop to any <code>&lt;button&gt;</code>
          component to make buttons look inactive. Disabled button has
          <code>pointer-events: none</code> applied to, disabling hover and active states from
          triggering.
        </p>
        <app-docs-example href="components/button#disabled-state">
          <button cButton color="primary" disabled size="lg">
            Primary button
          </button>
          <button cButton color="secondary" disabled size="lg">
            Button
          </button>
        </app-docs-example>
        <p class="text-body-secondary small">
          Disabled buttons using the <code>&lt;a&gt;</code> component act a little different:
        </p>
        <p class="text-body-secondary small">
          <code>&lt;a&gt;</code>s don&#39;tsupport the <code>disabled</code> attribute, so
          CoreUI has to add <code>.disabled</code> class to make buttons look inactive.
          CoreUI also has to add to the disabled button component
          <code>aria-disabled=&#34;true&#34;</code> attribute to show the state of the component
          to assistive technologies.
        </p>
        <app-docs-example href="components/button#disabled-state">
          <a [routerLink]="[]" cButton color="primary" disabled size="lg">
            Primary link
          </a>
          <a [routerLink]="[]" cButton color="secondary" disabled size="lg">
            Link
          </a>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button</strong> <small>Block buttons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Create buttons that span the full width of a parent—by using utilities.
        </p>
        <app-docs-example href="components/button#block-buttons">
          <div class="d-grid gap-2">
            <button cButton color="primary">Button</button>
            <button cButton color="primary">Button</button>
          </div>
        </app-docs-example>
        <p class="text-body-secondary small">
          Here we create a responsive variation, starting with vertically stacked buttons until
          the <code>md</code> breakpoint, where <code>.d-md-block</code> replaces the
          <code>.d-grid</code> class, thus nullifying the <code>gap-2</code> utility. Resize
          your browser to see them change.
        </p>
        <app-docs-example href="components/button#block-buttons">
          <div class="d-grid gap-2 d-md-block">
            <button cButton color="primary">Button</button>
            <button cButton color="primary">Button</button>
          </div>
        </app-docs-example>
        <p class="text-body-secondary small">
          You can adjust the width of your block buttons with grid column width classes. For
          example, for a half-width &#34;block button&#34;, use <code>.col-6</code>. Center it
          horizontally with <code>.mx-auto</code>, too.
        </p>
        <app-docs-example href="components/button#block-buttons">
          <div class="d-grid gap-2 col-6 mx-auto">
            <button cButton color="primary">Button</button>
            <button cButton color="primary">Button</button>
          </div>
        </app-docs-example>
        <p class="text-body-secondary small">
          Additional utilities can be used to adjust the alignment of buttons when horizontal.
          Here we&#39;ve taken our previous responsive example and added some flex utilities and
          a margin utility on the button to right align the buttons when they&#39;re no longer
          stacked.
        </p>
        <app-docs-example href="components/button#block-buttons">
          <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <button cButton class="me-md-2" color="primary">
              Button
            </button>
            <button cButton color="primary">Button</button>
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
