import { CommonModule } from '@angular/common';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-register-simple',
  standalone: true,
  templateUrl: './register-simple.component.html',
  imports: [FormsModule, CommonModule, RouterModule],
})
export class RegisterSimpleComponent {
  username = '';
  email = '';
  password = '';
  repeatPassword = '';
  loading = false;
  toastMsg: string | null = null;
  toastType: 'success' | 'error' = 'success';

  constructor(private http: HttpClient, private router: Router) {}

  showToast(msg: string, type: 'success' | 'error' = 'success') {
    this.toastMsg = msg;
    this.toastType = type;
    setTimeout(() => {
      this.toastMsg = null;
    }, 3000);
  }

  register() {
    if (!this.username || !this.password || !this.repeatPassword) {
      this.showToast('<PERSON>ui lòng nhập đầy đủ thông tin', 'error');
      return;
    }
    if (this.password !== this.repeatPassword) {
      this.showToast('Mật khẩu nhập lại không khớp', 'error');
      return;
    }
    if (this.password.length < 6) {
      this.showToast('Mật khẩu phải từ 6 ký tự', 'error');
      return;
    }
    this.loading = true;
    const params = new HttpParams()
      .set('username', this.username)
      .set('password', this.password);
    this.http.post<any>('/auth/register', {}, { params }).subscribe({
      next: (res) => {
        this.showToast(res.message || 'Đăng ký thành công!', 'success');
        setTimeout(() => this.router.navigate(['/login']), 1200);
      },
      error: (err) => {
        this.showToast(err.error?.message || 'Đăng ký thất bại', 'error');
        this.loading = false;
      },
      complete: () => {
        this.loading = false;
      },
    });
  }
}
