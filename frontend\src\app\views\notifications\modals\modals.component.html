<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <app-docs-components href="components/modal" title="Modal" />
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Below is a static modal example (meaning its <code>position</code> and
          <code>display</code> have been overridden). Included are the modal header, modal body
          (required for <code>padding</code>), and modal footer (optional). We ask that you
          include modal headers with dismiss actions whenever possible, or provide another
          explicit dismiss action.
        </p>
        <app-docs-example href="components/modal">
          <c-modal
            [keyboard]="false"
            [transition]="false"
            backdrop="static"
            class="position-static d-block show"
            id="modalStatic"
          >
            <c-modal-header>
              <h5 cModalTitle>Modal title</h5>
              <button cButtonClose></button>
            </c-modal-header>
            <c-modal-body>Modal body text goes here.</c-modal-body>
            <c-modal-footer>
              <button cButton color="secondary">Close</button>
              <button cButton color="primary">Save changes</button>
            </c-modal-footer>
          </c-modal>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Live demo</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Toggle a working modal demo by clicking the button below. It will slide down and fade
          in from the top of the page.
        </p>
        <app-docs-example href="components/modal#live-demo">
          <ng-container *ngTemplateOutlet="liveDemo" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Static backdrop</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          If you don’t provide an <code>(visibleChange)</code> handler to the Modal component, your
          modal will behave as though the backdrop is static, meaning it will not close when
          clicking outside it. Click the button below to try it.
        </p>
        <app-docs-example href="components/modal#static-backdrop">
          <ng-container *ngTemplateOutlet="staticBackdrop" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Scrolling long content</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          If your modals are too long for the user’s viewport, they scroll the page by itself.
        </p>
        <app-docs-example href="components/modal#scrolling-long-content">
          <ng-container *ngTemplateOutlet="scrollingLongContent" />
        </app-docs-example>
        <p class="text-body-secondary small">
          You can also create a scrollable modal that allows scroll the modal body by adding <code>scrollable</code>
          prop.
        </p>
        <app-docs-example href="components/modal#scrolling-long-content">
          <ng-container *ngTemplateOutlet="scrollableLongContent" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Vertically centered</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>alignment=&#34;center&#34;</code> to <code>&lt;c-modal&gt;</code> to
          vertically center the modal.
        </p>
        <app-docs-example href="components/modal#vertically-centered">
          <ng-container *ngTemplateOutlet="verticallyCentered" />
        </app-docs-example>
        <app-docs-example href="components/modal#vertically-centered">
          <ng-container *ngTemplateOutlet="verticallyCenteredScrollable" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Tooltips and popovers</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          <code>cTooltip</code> and <code>cPopover</code> can be placed within
          modals as needed. When modals are closed, any tooltips and popovers within are also
          automatically dismissed.
        </p>
        <app-docs-example href="components/modal#tooltips-and-popovers">
          <ng-container *ngTemplateOutlet="withPopover" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Optional sizes</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Modals have three optional sizes, available via modifier classes to be placed on a
          <code>&lt;c-modal&gt;</code>. These sizes kick in at certain breakpoints to avoid
          horizontal scrollbars on narrower viewports.
        </p>
        <table class="table">
          <thead>
          <tr>
            <th>Size</th>
            <th>Property size</th>
            <th>Modal max-width</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>Small</td>
            <td>
              <code>&#39;sm&#39;</code>
            </td>
            <td>
              <code>300px</code>
            </td>
          </tr>
          <tr>
            <td>Default</td>
            <td class="text-body-secondary">None</td>
            <td>
              <code>500px</code>
            </td>
          </tr>
          <tr>
            <td>Large</td>
            <td>
              <code>&#39;lg&#39;</code>
            </td>
            <td>
              <code>800px</code>
            </td>
          </tr>
          <tr>
            <td>Extra large</td>
            <td>
              <code>&#39;xl&#39;</code>
            </td>
            <td>
              <code>1140px</code>
            </td>
          </tr>
          </tbody>
        </table>
        <app-docs-example href="components/modal#optional-sizes">
          <ng-container *ngTemplateOutlet="optionalSizes" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Modal</strong> <small>Fullscreen Modal</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Another override is the option to pop up a modal that covers the user viewport,
          available via property <code>fullscreen</code>.
        </p>
        <table class="table">
          <thead>
          <tr>
            <th>Property fullscreen</th>
            <th>Availability</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>
              <code>true</code>
            </td>
            <td>Always</td>
          </tr>
          <tr>
            <td>
              <code>&#39;sm&#39;</code>
            </td>
            <td>
              Below <code>576px</code>
            </td>
          </tr>
          <tr>
            <td>
              <code>&#39;md&#39;</code>
            </td>
            <td>
              Below <code>768px</code>
            </td>
          </tr>
          <tr>
            <td>
              <code>&#39;lg&#39;</code>
            </td>
            <td>
              Below <code>992px</code>
            </td>
          </tr>
          <tr>
            <td>
              <code>&#39;xl&#39;</code>
            </td>
            <td>
              Below <code>1200px</code>
            </td>
          </tr>
          <tr>
            <td>
              <code>&#39;xxl&#39;</code>
            </td>
            <td>
              Below <code>1400px</code>
            </td>
          </tr>
          </tbody>
        </table>
        <app-docs-example href="components/modal#fullscreen-modal">
          <ng-container *ngTemplateOutlet="fullScreen" />
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

<ng-template #liveDemo>
  <button (click)="toggleLiveDemo()" cButton>Launch demo modal</button>
  <c-modal id="liveDemoModal" [visible]="liveDemoVisible" (visibleChange)="handleLiveDemoChange($event)">
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button (click)="toggleLiveDemo()" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>Woohoo, you&#39;re reading this text in a modal!</c-modal-body>
    <c-modal-footer>
      <button (click)="toggleLiveDemo()" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Save changes</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #staticBackdrop>
  <button [cModalToggle]="staticBackdropModal.id" cButton>Launch static backdrop modal</button>
  <c-modal #staticBackdropModal backdrop="static" id="staticBackdropModal" [@.disabled]="false">
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button [cModalToggle]="staticBackdropModal.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>I will not close if you click outside of me. Don't even try to press escape key.</c-modal-body>
    <c-modal-footer>
      <button [cModalToggle]="staticBackdropModal.id" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Understood</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #scrollingLongContent>
  <button [cModalToggle]="scrollingLongContentModal.id" cButton>Scrolling long content</button>
  <c-modal #scrollingLongContentModal id="scrollingLongContentModal">
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button [cModalToggle]="scrollingLongContentModal.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>
      <ng-container *ngTemplateOutlet="longContent" />
    </c-modal-body>
    <c-modal-footer>
      <button [cModalToggle]="scrollingLongContentModal.id" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Save changes</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #scrollableLongContent>
  <button [cModalToggle]="scrollableLongContentModal.id" cButton>Scrollable long content</button>
  <c-modal #scrollableLongContentModal id="scrollableLongContentModal" scrollable>
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button [cModalToggle]="scrollableLongContentModal.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>
      <ng-container *ngTemplateOutlet="longContent" />
    </c-modal-body>
    <c-modal-footer>
      <button [cModalToggle]="scrollableLongContentModal.id" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Save changes</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #verticallyCentered>
  <button [cModalToggle]="verticallyCenteredModal.id" cButton>Centered modal</button>
  <c-modal #verticallyCenteredModal alignment="center" id="verticallyCenteredModal">
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button [cModalToggle]="verticallyCenteredModal.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>
      Woohoo, you&#39;re reading this text in a modal!
    </c-modal-body>
    <c-modal-footer>
      <button [cModalToggle]="verticallyCenteredModal.id" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Understood</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #verticallyCenteredScrollable>
  <button [cModalToggle]="verticallyCenteredScrollableModal.id" cButton>Centered scrollable modal</button>
  <c-modal #verticallyCenteredScrollableModal
           [scrollable]="true"
           alignment="center"
           id="verticallyCenteredScrollableModal">
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button [cModalToggle]="verticallyCenteredScrollableModal.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>
      <p>
        This is some placeholder content to show a vertically centered modal. We've added some extra copy here to show
        how vertically centering the modal works when combined with scrollable modals. We also use some repeated line
        breaks to quickly extend the height of the content, thereby triggering the scrolling. When content becomes
        longer than the predefined max-height of modal, content will be cropped and scrollable within the modal.
      </p>
      <br><br><br><br><br><br><br><br><br><br>
      <p>Just like that.</p>
    </c-modal-body>
    <c-modal-footer>
      <button [cModalToggle]="verticallyCenteredScrollableModal.id" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Understood</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #withPopover>
  <button [cModalToggle]="withPopoverModal.id" cButton>Modal with Popover</button>
  <c-modal #withPopoverModal alignment="center" id="withPopoverModal">
    <c-modal-header>
      <h5 cModalTitle>Modal title</h5>
      <button [cModalToggle]="withPopoverModal.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>
      <h5>Popover in a modal</h5>
      This
      <button [cPopoverTrigger]="'click'" [cPopover]="popoverHtml" [cPopoverOptions]="{strategy: 'fixed'}" cButton>
        button
      </button>
      triggers a popover on click.
      <ng-template #popoverHtml>
        <h3 class="popover-header">
          Popover title
        </h3>
        <div class="popover-body" id="">
          And here’s some amazing content. It’s very engaging. Right?
        </div>
      </ng-template>
      <hr />
      <h5>Tooltips in a modal</h5>
      <p>
        <a cTooltip="Tooltip text" href>This link</a> and <a [cTooltipOptions]="{strategy: 'fixed'}"
                                                             cTooltip="Tooltip text"
                                                             cTooltipPlacement="bottom"
                                                             href>that link</a>
        have tooltips on hover.
      </p>
    </c-modal-body>
    <c-modal-footer>
      <button [cModalToggle]="withPopoverModal.id" cButton color="secondary">
        Close
      </button>
      <button cButton color="primary">Understood</button>
    </c-modal-footer>
  </c-modal>
</ng-template>

<ng-template #optionalSizes>
  <button [cModalToggle]="modalXl.id" cButton>Extra large modal</button>
  <button [cModalToggle]="modalLg.id" cButton>Large modal</button>
  <button [cModalToggle]="modalSm.id" cButton>Small modal</button>
  <c-modal #modalXl id="modalXl" size="xl">
    <c-modal-header>
      <h5 cModalTitle>Extra large modal</h5>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
  <c-modal #modalLg id="modalLg" size="lg">
    <c-modal-header>
      <h5 cModalTitle>Large modal</h5>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
  <c-modal #modalSm id="modalSm" size="sm">
    <c-modal-header>
      <h5 cModalTitle>Small modal</h5>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
</ng-template>

<ng-template #fullScreen>
  <button [cModalToggle]="fullScreen.id" cButton>Full screen</button>
  <button [cModalToggle]="fullScreenSm.id" cButton>Full screen below sm</button>
  <button [cModalToggle]="fullScreenMd.id" cButton>Full screen below md</button>
  <button [cModalToggle]="fullScreenLg.id" cButton>Full screen below lg</button>
  <button [cModalToggle]="fullScreenXl.id" cButton>Full screen below xl</button>
  <button [cModalToggle]="fullScreen2Xl.id" cButton>Full screen below xxl</button>
  <c-modal #fullScreen [fullscreen]="true" id="fullScreen">
    <c-modal-header>
      <h5 cModalTitle>Full screen</h5>
      <button [cModalToggle]="fullScreen.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
    <c-modal-footer>
      <button cButton [cModalToggle]="fullScreen.id">Close</button>
    </c-modal-footer>
  </c-modal>
  <c-modal #fullScreenSm fullscreen="sm" id="fullScreenSm">
    <c-modal-header>
      <h5 cModalTitle>Full screen below sm</h5>
      <button [cModalToggle]="fullScreenSm.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
  <c-modal #fullScreenMd fullscreen="md" id="fullScreenMd">
    <c-modal-header>
      <h5 cModalTitle>Full screen below md</h5>
      <button [cModalToggle]="fullScreenMd.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
  <c-modal #fullScreenLg fullscreen="lg" id="fullScreenLg">
    <c-modal-header>
      <h5 cModalTitle>Full screen below lg</h5>
      <button [cModalToggle]="fullScreenLg.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
  <c-modal #fullScreenXl fullscreen="xl" id="fullScreenXl">
    <c-modal-header>
      <h5 cModalTitle>Full screen below xl</h5>
      <button [cModalToggle]="fullScreenXl.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
  <c-modal #fullScreen2Xl fullscreen="xxl" id="fullScreen2Xl">
    <c-modal-header>
      <h5 cModalTitle>Full screen below xxl</h5>
      <button [cModalToggle]="fullScreen2Xl.id" cButtonClose></button>
    </c-modal-header>
    <c-modal-body>...</c-modal-body>
  </c-modal>
</ng-template>

<ng-template #longContent>
  <p>
    Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis
    in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
  </p>
  <p>
    Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis
    lacus vel augue laoreet rutrum faucibus dolor auctor.
  </p>
  <p>
    Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel
    scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus
    auctor fringilla.
  </p>
  <p>
    Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis
    in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
  </p>
  <p>
    Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis
    lacus vel augue laoreet rutrum faucibus dolor auctor.
  </p>
  <p>
    Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel
    scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus
    auctor fringilla.
  </p>
  <p>
    Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis
    in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
  </p>
  <p>
    Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis
    lacus vel augue laoreet rutrum faucibus dolor auctor.
  </p>
  <p>
    Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel
    scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus
    auctor fringilla.
  </p>
  <p>
    Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis
    in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
  </p>
  <p>
    Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis
    lacus vel augue laoreet rutrum faucibus dolor auctor.
  </p>
  <p>
    Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel
    scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus
    auctor fringilla.
  </p>
  <p>
    Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis
    in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
  </p>
  <p>
    Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis
    lacus vel augue laoreet rutrum faucibus dolor auctor.
  </p>
  <p>
    Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel
    scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus
    auctor fringilla.
  </p>
  <p>
    Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis
    in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros.
  </p>
  <p>
    Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis
    lacus vel augue laoreet rutrum faucibus dolor auctor.
  </p>
  <p>
    Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel
    scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus
    auctor fringilla.
  </p>
</ng-template>
